/* Alarm Modal Styles */
.alarm-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.alarm-modal-content {
    background-color: var(--card-bg, #fff);
    color: var(--text-color, #000);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    text-align: center;
    max-width: 400px;
    width: 90%;
}

.alarm-modal-content h2 {
    margin: 0 0 1rem;
    font-size: 1.5rem;
}

.alarm-modal-content p {
    font-size: 2rem;
    margin: 1rem 0;
    font-weight: bold;
    color: var(--primary-color, #fe2c55);
}

.alarm-modal-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 1.5rem;
}

.alarm-modal-buttons button {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.snooze-btn {
    background-color: var(--secondary-color, #25f4ee);
    color: white;
}

.snooze-btn:hover {
    background-color: #1cd8d2;
}

.stop-btn {
    background-color: var(--primary-color, #fe2c55);
    color: white;
}

.stop-btn:hover {
    background-color: #e61e4d;
}

/* Notification Styles */
.gpace-notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: var(--card-bg, #fff);
    color: var(--text-color, #000);
    padding: 1rem 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 9998;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Mini Alarm Display */
.mini-alarm-display {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background-color: var(--card-bg, #fff);
    color: var(--text-color, #000);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    font-size: 0.9rem;
    z-index: 9997;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.mini-alarm-display .time {
    font-weight: bold;
    color: var(--primary-color, #fe2c55);
}

.mini-alarm-display .icon {
    color: var(--secondary-color, #25f4ee);
}

/* Dark Theme Support */
[data-theme="dark"] .alarm-modal-content {
    background-color: #1e1e1e;
    color: #ffffff;
}

[data-theme="dark"] .gpace-notification {
    background-color: #1e1e1e;
    color: #ffffff;
}

[data-theme="dark"] .mini-alarm-display {
    background-color: #1e1e1e;
    color: #ffffff;
}

/* Mini Clock Display */
.mini-clock-display {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: var(--card-bg, #1e1e1e);
    color: var(--text-color, #ffffff);
    padding: 10px 15px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    font-family: 'Arial', sans-serif;
    z-index: 999;
    display: flex;
    flex-direction: column;
    gap: 5px;
    min-width: 120px;
    transition: all 0.3s ease;
    margin-bottom: 60px;
}

.mini-clock-display:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.mini-time {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2px;
}

.mini-hour,
.mini-minute {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color, #fe2c55);
}

.mini-colon {
    font-size: 1.2rem;
    color: var(--text-color, #ffffff);
    opacity: 0.7;
    animation: blink 1s infinite;
    margin: 0 2px;
}

.mini-ampm {
    font-size: 0.8rem;
    color: var(--secondary-color, #25f4ee);
    margin-left: 4px;
}

.mini-next-alarm {
    font-size: 0.8rem;
    color: var(--text-color, #ffffff);
    opacity: 0.8;
    text-align: center;
}

/* Light theme support */
[data-theme="light"] .mini-clock-display,
.light-theme .mini-clock-display {
    background: var(--card-bg, #ffffff);
    color: var(--text-color, #121212);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

/* Enhanced notification container */
.alarm-notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 998;
    display: flex;
    flex-direction: column;
    gap: 10px;
    pointer-events: none;
    margin-top: 60px;
}

.alarm-notification {
    background: var(--card-bg, #1e1e1e);
    color: var(--text-color, #ffffff);
    padding: 15px 20px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    animation: slideInRight 0.3s ease-out;
    pointer-events: auto;
    max-width: 300px;
}

.alarm-notification i {
    font-size: 1.5rem;
    color: var(--primary-color, #fe2c55);
}

.alarm-notification-content {
    flex: 1;
}

.alarm-notification-title {
    font-weight: 600;
    margin-bottom: 5px;
}

.alarm-notification-message {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Enhanced wave animation */
.wave-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.8);
    width: 200px;
    height: 200px;
    display: none;
    z-index: 997;
    pointer-events: none;
}

.wave-container.active {
    display: block;
}

.wave {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: var(--primary-color, #fe2c55);
    opacity: 0;
    animation: wave 2s infinite;
}

.wave:nth-child(2) {
    animation-delay: 0.5s;
}

.wave:nth-child(3) {
    animation-delay: 1s;
}

@keyframes wave {
    0% {
        transform: scale(0);
        opacity: 0.5;
    }
    100% {
        transform: scale(2);
        opacity: 0;
    }
}

/* Quick Add Modal Styles */
.quick-add .alarm-modal-content {
    max-width: 500px;
}

.quick-add-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.time-input {
    display: flex;
    gap: 10px;
}

.time-input input[type="time"] {
    flex: 1;
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    background: var(--background-color);
    color: var(--text-color);
}

.day-selector {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: center;
    padding: 10px;
    background: var(--background-color);
    border-radius: 8px;
}

.day-selector label {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    border-radius: 15px;
    background: var(--card-bg);
    cursor: pointer;
    transition: all 0.2s ease;
}

.day-selector label:hover {
    background: var(--hover-bg);
}

.day-selector input[type="checkbox"]:checked + label {
    background: var(--primary-color);
    color: white;
}

/* Template Modal Styles */
.template-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.template-item {
    background: var(--background-color);
    padding: 15px;
    border-radius: 10px;
    text-align: center;
}

.template-item h4 {
    color: var(--primary-color);
    margin-bottom: 10px;
}

.template-item button {
    margin-top: 10px;
    padding: 8px 15px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.template-item button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Bulk Add Modal Styles */
.bulk-add .alarm-modal-content {
    max-width: 700px;
}

.bulk-add-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin: 20px 0;
}

.csv-upload {
    padding: 20px;
    background: var(--background-color);
    border-radius: 10px;
    text-align: center;
}

.csv-upload input[type="file"] {
    margin-top: 15px;
}

.manual-bulk-add {
    padding: 20px;
    background: var(--background-color);
    border-radius: 10px;
}

.bulk-alarm-entry {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    align-items: center;
}

.bulk-alarm-entry input[type="time"] {
    flex: 1;
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    background: var(--card-bg);
    color: var(--text-color);
}

.bulk-alarm-entry select {
    width: 80px;
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    background: var(--card-bg);
    color: var(--text-color);
}

.bulk-alarm-entry input[type="text"] {
    flex: 2;
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    background: var(--card-bg);
    color: var(--text-color);
}

.remove-entry {
    background: var(--primary-color);
    color: white;
    border: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

#addMoreAlarms {
    width: 100%;
    padding: 10px;
    margin-top: 15px;
    background: var(--secondary-color);
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.2s ease;
}

#addMoreAlarms:hover {
    opacity: 0.9;
}

/* Keyboard Shortcut Tooltip */
.keyboard-shortcut {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--card-bg);
    padding: 10px 20px;
    border-radius: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    display: flex;
    gap: 15px;
    font-size: 14px;
    z-index: 1000;
}

.keyboard-shortcut span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.keyboard-shortcut kbd {
    background: var(--background-color);
    padding: 2px 6px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
}

/* Enhanced Alarm Item Styles */
.alarm-item {
    border-left: 4px solid var(--primary-color);
    transition: all 0.2s ease;
}

.alarm-item[data-priority="high"] {
    border-left-color: #ff4444;
}

.alarm-item[data-priority="normal"] {
    border-left-color: #ffbb33;
}

.alarm-item[data-priority="low"] {
    border-left-color: #00C851;
}

.alarm-item.recurring::before {
    content: '↻';
    position: absolute;
    right: 10px;
    top: 10px;
    color: var(--secondary-color);
    font-size: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .bulk-add-options {
        grid-template-columns: 1fr;
    }

    .keyboard-shortcut {
        display: none;
    }

    .day-selector {
        justify-content: flex-start;
        overflow-x: auto;
        padding: 10px 5px;
    }

    .day-selector label {
        flex: 0 0 auto;
    }
} 