/* Settings Page Styles */
:root {
    --primary-color: #fe2c55;
    --secondary-color: #25f4ee;
    --background-color: #121212;
    --text-color: #ffffff;
    --card-bg: #1e1e1e;
    --hover-bg: #2d2d2d;
    --nav-bg: #1a1a1a;
    --border-color: #333;
}

body {
    background-color: var(--background-color);
    color: var(--text-color);
    transition: all 0.3s ease;
    min-height: 100vh;
    padding-top: 60px;
}

.top-nav {
    background-color: var(--nav-bg);
    padding: 10px 30px; /* Match extracted.html */
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    height: 60px; /* Match extracted.html */
    backdrop-filter: blur(10px); /* Match extracted.html */
}

.nav-brand {
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
}

.nav-links {
    display: flex;
    gap: 20px;
}

.nav-links a {
    color: var(--text-color);
    text-decoration: none;
    padding: 5px 10px;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.nav-links a:hover {
    background-color: var(--hover-bg);
}

.nav-links a.active {
    background-color: var(--primary-color);
    color: white;
}

.container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.main-header {
    background-color: var(--card-bg);
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.logo-section h1 {
    color: var(--primary-color);
    font-size: 48px;
    margin-bottom: 10px;
}

.tagline {
    color: var(--secondary-color);
    font-size: 20px;
}

.account-section {
    margin-top: 20px;
}

.login-btn, .interactive-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.login-btn:hover, .interactive-button:hover {
    opacity: 0.9;
}

/* Using the global logout-btn styling from main.css */

.todoist-section {
    background-color: var(--card-bg);
    border-radius: 12px;
    padding: 30px;
    margin-top: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.tasks-container {
    margin-top: 20px;
}

.tasks-list {
    background-color: var(--hover-bg);
    border-radius: 8px;
    padding: 15px;
}

.sound-controls {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.sound-toggle {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    padding: 10px;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.3s;
}

.sound-toggle:hover {
    background-color: var(--hover-bg);
}

/* Theme toggle button */
.theme-toggle {
    position: fixed;
    bottom: 20px;
    left: 20px;
    z-index: 1001;
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    font-size: 14px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.theme-toggle:hover {
    background-color: var(--hover-bg);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.theme-icon {
    font-size: 16px;
}

/* Light theme */
body.light-theme {
    --background-color: #f5f5f5;
    --text-color: #333333;
    --card-bg: #ffffff;
    --hover-bg: #f0f0f0;
    --nav-bg: #ffffff;
    --border-color: #e0e0e0;
}

.quotes-section, .role-models-section {
    background-color: var(--card-bg);
    border-radius: 12px;
    padding: 30px;
    margin-top: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.quote-input-container, .role-model-input-container {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.quote-input-container input,
.role-model-input-container input {
    flex-grow: 1;
    background-color: var(--hover-bg);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    padding: 10px;
    border-radius: 5px;
}

.quote-list, .role-model-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}

.quote-card, .role-model-card {
    background-color: var(--hover-bg);
    border-radius: 8px;
    padding: 15px;
    position: relative;
}

.quote-image, .role-model-image {
    max-width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 10px;
}

.delete-quote, .delete-role-model {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
}

.role-model-research {
    margin-top: 20px;
    background-color: var(--hover-bg);
    border-radius: 8px;
    padding: 15px;
}

.role-model-research-content {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}

.role-model-research-card {
    background-color: var(--card-bg);
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.research-loading {
    text-align: center;
    color: var(--secondary-color);
    padding: 20px;
}

.settings-section {
    margin-top: 20px;
}

.task-navigation-card {
    background-color: var(--card-bg);
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.task-navigation-card:hover {
    transform: scale(1.02);
}

.task-portal-intro h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
}

.task-portal-btn {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    padding: 15px 25px;
    margin: 20px 0;
    background-color: var(--primary-color);
    border: none;
    border-radius: 10px;
}

.task-portal-btn .btn-subtext {
    font-size: 0.7em;
    color: rgba(255,255,255,0.7);
    margin-top: 5px;
}

.task-portal-features {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-top: 20px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--text-color);
    opacity: 0.8;
}

.feature-item i {
    font-size: 1.5em;
    color: var(--primary-color);
}

/* Error message styling */
.error-message {
    background-color: rgba(255, 0, 0, 0.1);
    border: 1px solid rgba(255, 0, 0, 0.3);
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
    color: var(--text-color);
}

/* Spinner animation */
.spinner {
    animation: spin 2s linear infinite;
    display: inline-block;
    font-size: 24px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Role model research preview */
.role-model-research-preview {
    margin-top: 10px;
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 5px;
}

/* Role model actions */
.role-model-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    justify-content: space-between;
}

/* Quote text styling */
.quote-text {
    font-style: italic;
    margin-bottom: 5px;
}

.quote-author {
    text-align: right;
    font-weight: bold;
    color: var(--secondary-color);
}
