<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPAce - Your Academic Assistant</title>
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../../assets/images/gpace-logo-white.png">
    <link rel="shortcut icon" type="image/png" href="../../assets/images/gpace-logo-white.png">

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="../../css/settings.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="top-nav">
        <div class="nav-brand d-flex align-items-center">
            <img
                src="../../assets/images/gpace-logo-white.png"
                alt="GPAce Logo"
                style="height: 80px; margin-right: 0px;"
            >
            <a href="grind.html" style="text-decoration: none; color: inherit;">GPAce</a>
        </div>
        <div class="nav-links">
            <a href="grind.html">Grind Mode</a>
            <a href="study-spaces.html">Grind Station</a>
            <a href="daily-calendar.html">Daily Drip</a>
            <a href="academic-details.html">Brain Juice</a>
            <a href="extracted.html">Hustle Hub</a>
            <a href="subject-marks.html">Subject Marks</a>
            <a href="flashcards.html">Flashcards</a>
            <a href="settings.html" class="active">Settings</a>
        </div>
    </nav>
    <!-- Sound Controls -->
    <div class="sound-controls">
        <button id="toggleSound" class="sound-toggle" aria-label="Toggle Sound">
            <span class="sound-icon">🔊</span>
        </button>
    </div>

    <!-- Main Content Container -->
    <div class="container">

        <section class="settings-section tasks-management">
            <div class="container">
                <h2>🔗 Todoist Integration Hub</h2>
                <div class="task-navigation-card">
                    <div class="task-portal-intro">
                        <h3>Sync Your Productivity</h3>
                        <p>Seamlessly connect and manage your Todoist tasks within GPAce</p>
                    </div>
                    <div class="task-portal-actions">
                        <a href="tasks.html" class="btn btn-primary task-portal-btn">
                            <i class="bi bi-link-45deg"></i> Connect Todoist Account
                            <span class="btn-subtext">Unlock Unified Task Management</span>
                        </a>
                    </div>
                    <div class="task-portal-features">
                        <div class="feature-item">
                            <i class="bi bi-cloud-sync"></i>
                            <span>Real-time Synchronization</span>
                        </div>
                        <div class="feature-item">
                            <i class="bi bi-list-task"></i>
                            <span>Task Prioritization</span>
                        </div>
                        <div class="feature-item">
                            <i class="bi bi-calendar-check"></i>
                            <span>Academic Task Tracking</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <div class="quotes-section">
            <h2>Quotes Management</h2>
            <div class="quote-input-container">
                <input type="text" id="quoteText" placeholder="Enter quote text">
                <input type="text" id="quoteAuthor" placeholder="Quote author">
                <input type="text" id="quoteImage" placeholder="Optional image URL">
                <button class="interactive-button">Add Quote</button>
            </div>
            <div id="quoteList" class="quote-list"></div>
        </div>

        <div class="role-models-section">
            <h2>Role Models</h2>
            <div class="role-model-input-container">
                <input type="text" id="roleModelName" placeholder="Role model name">
                <input type="text" id="roleModelImage" placeholder="Role model image URL">
                <button class="interactive-button">Add Role Model</button>
            </div>
            <div id="roleModelList" class="role-model-list"></div>
            <div id="roleModelResearch" class="role-model-research"></div>
        </div>
    </div>

    <!-- Sound Manager -->
    
    <!-- Transition Manager -->
    
    <!-- Todoist Integration -->
    
    <!-- Main App Logic -->
    
    
    
    
    <!-- Theme Manager -->
    
    <!-- Quote Manager -->
    
    <!-- Role Model Manager -->
    
    
    
    
    
    
    
    
    
    
    
</body>
</html>
    
    
    
    
    
    
    
    
    
    
    
</body>
</html>
    <!-- Scripts -->
    <script src="../../js/soundManager.js"></script>
    <script src="../../js/transitionManager.js"></script>
    <script src="../../js/todoistIntegration.js"></script>
    <script src="../../js/app.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="../../js/gemini-api.js"></script>
    <script src="../../js/themeManager.js"></script>
    <script src="../../js/quoteManager.js"></script>
    <script src="../../js/roleModelManager.js"></script>
    <script src="../../js/inject-header.js"></script>
</body>
</html>