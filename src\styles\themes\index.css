/* Theme Index - Import all theme files */

/* Import light theme (default) */
@import './light.css';

/* Import dark theme */
@import './dark.css';

/* Theme transition animations */
* {
    transition: background-color var(--transition-speed) ease-in-out,
                color var(--transition-speed) ease-in-out,
                border-color var(--transition-speed) ease-in-out,
                box-shadow var(--transition-speed) ease-in-out;
}

/* Theme toggle button styling */
.theme-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--card-background);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    z-index: 1000;
    box-shadow: 0 2px 5px var(--shadow-color);
    transition: all var(--transition-speed) ease-in-out;
}

.theme-toggle:hover {
    background: var(--hover-color);
    transform: translateY(-1px);
}

.theme-toggle .icon {
    font-size: 1.2em;
    transition: transform var(--transition-speed) ease-in-out;
}

.theme-toggle:hover .icon {
    transform: rotate(180deg);
}

/* Theme-aware focus styles */
:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Theme-aware selection styles */
::selection {
    background-color: var(--primary-color);
    color: white;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    * {
        transition: none !important;
        animation: none !important;
    }
}
