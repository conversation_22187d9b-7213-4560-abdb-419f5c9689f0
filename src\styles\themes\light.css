/* Light Theme Variables */
:root {
    /* Light theme variables */
    --primary-color: #4A90E2;
    --secondary-color: #2ECC71;
    --accent-color: #F39C12;
    --text-color: #2C3E50;
    --background-color: #F5F7FA;
    --card-background: #FFFFFF;
    --border-color: #E1E4E8;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --hover-color: #F8F9FA;
    --transition-speed: 0.3s;
    
    /* Navigation specific */
    --nav-bg: var(--card-background);
    
    /* Text variations */
    --text-secondary: #666;
    --text-muted: #888;
    
    /* Status colors */
    --success-color: #28a745;
    --warning-color: #ffc107;
    --error-color: #dc3545;
    --info-color: #17a2b8;
}

/* Light theme specific component overrides */
.theme-toggle {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    color: var(--text-color);
}

.sound-toggle {
    background: var(--card-background);
    border: 1px solid var(--border-color);
}

/* Light theme specific task priority colors */
.priority-1 { background-color: #808080; }
.priority-2 { background-color: #5297ff; }
.priority-3 { background-color: #ff9a14; }
.priority-4 { background-color: #ff4646; }

/* Light theme specific time slot colors */
.time-slot.class {
    background: #e3f2fd;
    border-left: 4px solid #1976d2;
}

.time-slot.free {
    background: #f1f8e9;
    border-left: 4px solid #689f38;
}

/* Light theme specific study day styling */
.study-day {
    background: #e3f2fd;
    color: #1976d2;
}

/* Light theme specific prime slot styling */
.prime-slot {
    background: #f1f8e9;
    color: #689f38;
}
