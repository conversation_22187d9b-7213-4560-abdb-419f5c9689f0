/* Dark Theme Variables */
[data-theme="dark"] {
    --primary-color: #5C9CE6;
    --secondary-color: #3EDB81;
    --accent-color: #FFA726;
    --text-color: #E1E1E1;
    --background-color: #1A1A1A;
    --card-background: #2D2D2D;
    --border-color: #404040;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --hover-color: #363636;
    --transition-speed: 0.3s;
    
    /* Navigation specific */
    --nav-bg: var(--card-background);
    
    /* Text variations */
    --text-secondary: #B0B0B0;
    --text-muted: #888;
    
    /* Status colors - adjusted for dark theme */
    --success-color: #4CAF50;
    --warning-color: #FF9800;
    --error-color: #F44336;
    --info-color: #2196F3;
}

/* Dark theme specific component overrides */
[data-theme="dark"] .theme-toggle {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .sound-toggle {
    background: var(--card-background);
    border: 1px solid var(--border-color);
}

/* Dark theme specific task priority colors */
[data-theme="dark"] .priority-1 { background-color: #A0A0A0; }
[data-theme="dark"] .priority-2 { background-color: #6BB6FF; }
[data-theme="dark"] .priority-3 { background-color: #FFB347; }
[data-theme="dark"] .priority-4 { background-color: #FF6B6B; }

/* Dark theme specific time slot colors */
[data-theme="dark"] .time-slot.class {
    background: #1E3A8A;
    border-left: 4px solid #3B82F6;
}

[data-theme="dark"] .time-slot.free {
    background: #166534;
    border-left: 4px solid #22C55E;
}

/* Dark theme specific study day styling */
[data-theme="dark"] .study-day {
    background: #1E3A8A;
    color: #60A5FA;
}

/* Dark theme specific prime slot styling */
[data-theme="dark"] .prime-slot {
    background: #166534;
    color: #4ADE80;
}

/* Dark theme scrollbar styling */
[data-theme="dark"] ::-webkit-scrollbar {
    width: 8px;
}

[data-theme="dark"] ::-webkit-scrollbar-track {
    background: var(--background-color);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
    background: var(--hover-color);
}
